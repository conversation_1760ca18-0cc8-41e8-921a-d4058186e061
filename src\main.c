#include "Dri_Timer0.h"
#include "Int_LEDMatrix.h"
#include "Int_EEPROM.h"
#include "Int_LCD1602.h"
#include <STC89C5xRC.H>

// 定义按钮SW3引脚 (假设连接到P3.2)
#define SW3 P32

u8 picture[26] = {0xF8, 0x0A, 0xEC, 0xAF, 0xEC, 0x8A, 0xF8, 0x00,
                  0x10, 0xF9, 0x97, 0xF1, 0x88, 0xAA, 0xFF, 0xAA,
                  0x88, 0x00, 0x14, 0x0A, 0xF5, 0x92, 0x92, 0xF5,
                  0x0A, 0x14};
u8 buffer[26];

// 按钮状态变量
bit sw3_pressed = 0;
bit sw3_last_state = 1;  // 按钮默认高电平

void main()
{
    u8 i;
    bit sw3_current_state;

    Dri_Timer0_Init();
    Int_LEDMatrix_Init();
    Int_LCD1602_Init();

    // 初始化LCD显示
    Int_LCD1602_ShowString(0, 0, "Hello LCD1602!");
    Int_LCD1602_ShowString(1, 0, "Press SW3 Clear");

    Int_EEPROM_WriteBytes(0, picture, 26);
    Int_EEPROM_ReadBytes(0, buffer, 26);

    while (1) {
        // 检测按钮SW3状态
        sw3_current_state = SW3;

        // 检测按钮按下（下降沿）
        if (sw3_last_state == 1 && sw3_current_state == 0) {
            sw3_pressed = 1;
            Com_Util_Delay1ms(20);  // 消抖延时
        }

        // 如果按钮被按下，执行清屏操作
        if (sw3_pressed) {
            Int_LCD1602_Clear();
            Int_LCD1602_ShowString(0, 0, "Screen Cleared!");
            Int_LCD1602_ShowString(1, 0, "Press SW3 Again");
            sw3_pressed = 0;  // 清除按钮标志
        }

        sw3_last_state = sw3_current_state;

        // LED矩阵显示
        for (i = 0; i < 26; i++) {
            Int_LEDMatrix_Shift(buffer[i]);
            Com_Util_Delay1ms(200);
        }
    }
}